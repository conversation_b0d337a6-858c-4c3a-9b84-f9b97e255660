import subprocess
import sys

def run_script(script_name):
    """运行指定的Python脚本"""
    try:
        print(f"开始运行 {script_name}...")
        # 执行脚本，使用当前Python解释器
        result = subprocess.run(
            [sys.executable, script_name],
            check=True,
            capture_output=True,
            text=True
        )
        # 打印脚本输出
        print(f"{script_name} 输出:\n{result.stdout}")
        print(f"{script_name} 运行成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"{script_name} 运行失败，返回代码: {e.returncode}")
        print(f"错误输出: {e.stderr}")
        return False
    except FileNotFoundError:
        print(f"未找到脚本文件: {script_name}")
        return False

def main():
    # 要运行的脚本列表，按顺序排列
    scripts = ["main.py", "mainV1.py", "mainV2.py"]
    
    for script in scripts:
        # 运行当前脚本，如果失败则停止后续执行
        if not run_script(script):
            print("由于前一个脚本运行失败，停止执行后续脚本")
            return
    
    print("所有脚本已成功运行完毕")

if __name__ == "__main__":
    main()
