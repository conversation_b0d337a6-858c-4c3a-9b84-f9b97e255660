import subprocess
import sys
import time  # 导入时间模块用于记录时间

def run_script(script_name):
    """运行指定的Python脚本"""
    try:
        print(f"开始运行 {script_name}...")
        # 执行脚本，使用当前Python解释器
        result = subprocess.run(
            [sys.executable, script_name],
            check=True,
            capture_output=True,
            text=True
        )
        # 打印脚本输出
        print(f"{script_name} 输出:\n{result.stdout}")
        print(f"{script_name} 运行成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"{script_name} 运行失败，返回代码: {e.returncode}")
        print(f"错误输出: {e.stderr}")
        return False
    except FileNotFoundError:
        print(f"未找到脚本文件: {script_name}")
        return False

def main():
    # 记录程序开始时间
    start_time = time.time()
    start_time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time))
    print(f"===== 程序开始运行时间: {start_time_str} =====")
    
    # 要运行的脚本列表，按顺序排列
    scripts = ["main.py", "mainV1.py", "mainV2.py"]
    
    for script in scripts:
        # 运行当前脚本，如果失败则停止后续执行
        if not run_script(script):
            print("由于前一个脚本运行失败，停止执行后续脚本")
            break
    
    # 记录程序结束时间并计算总时长
    end_time = time.time()
    end_time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(end_time))
    total_time = end_time - start_time  # 总时长（秒）
    
    # 格式化总时长（转换为时分秒）
    hours, remainder = divmod(int(total_time), 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print(f"\n===== 程序结束运行时间: {end_time_str} =====")
    print(f"===== 程序总运行时长: {hours}小时 {minutes}分钟 {seconds:.2f}秒 =====")

if __name__ == "__main__":
    main()