"""
Optimized Excel file loading utilities.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
import concurrent.futures
import time
from functools import lru_cache
import hashlib
import pickle
import os

from ..config import config
from .logger import Logger
from .cache import CacheManager


class ExcelLoader:
    """Optimized Excel file loader with caching and parallel processing."""
    
    def __init__(self, cache_enabled: bool = True):
        self.logger = Logger(__name__)
        self.cache_enabled = cache_enabled
        self.cache_manager = CacheManager() if cache_enabled else None
        self.cache_dir = Path(config.get('data.cache_dir', 'cache'))
        self.cache_dir.mkdir(exist_ok=True)
    
    def _get_file_hash(self, file_path: str) -> str:
        """Generate hash for file to detect changes."""
        file_path = Path(file_path)
        if not file_path.exists():
            return ""
        
        # Use file size and modification time for quick hash
        stat = file_path.stat()
        content = f"{file_path.name}_{stat.st_size}_{stat.st_mtime}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_cache_path(self, file_path: str) -> Path:
        """Get cache file path for given Excel file."""
        file_hash = self._get_file_hash(file_path)
        cache_name = f"{Path(file_path).stem}_{file_hash}.pkl"
        return self.cache_dir / cache_name
    
    def _load_from_cache(self, file_path: str) -> Optional[pd.DataFrame]:
        """Load DataFrame from cache if available and valid."""
        if not self.cache_enabled:
            return None
        
        cache_path = self._get_cache_path(file_path)
        if cache_path.exists():
            try:
                with open(cache_path, 'rb') as f:
                    df = pickle.load(f)
                self.logger.info(f"Loaded {file_path} from cache")
                return df
            except Exception as e:
                self.logger.warning(f"Failed to load cache for {file_path}: {e}")
                # Remove corrupted cache file
                cache_path.unlink(missing_ok=True)
        
        return None
    
    def _save_to_cache(self, file_path: str, df: pd.DataFrame) -> None:
        """Save DataFrame to cache."""
        if not self.cache_enabled:
            return
        
        cache_path = self._get_cache_path(file_path)
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(df, f)
            self.logger.debug(f"Saved {file_path} to cache")
        except Exception as e:
            self.logger.warning(f"Failed to save cache for {file_path}: {e}")
    
    def load_excel(self, 
                   file_path: str, 
                   sheet_name: Union[str, int] = 0,
                   **kwargs) -> pd.DataFrame:
        """
        Load Excel file with caching support.
        
        Args:
            file_path: Path to Excel file
            sheet_name: Sheet name or index to load
            **kwargs: Additional arguments for pd.read_excel
        
        Returns:
            DataFrame containing the Excel data
        """
        start_time = time.time()
        
        # Try to load from cache first
        df = self._load_from_cache(file_path)
        if df is not None:
            load_time = time.time() - start_time
            self.logger.info(f"Loaded {file_path} from cache in {load_time:.2f}s")
            return df
        
        # Load from Excel file
        try:
            self.logger.info(f"Loading {file_path} from disk...")
            
            # Optimize pandas read_excel parameters
            default_kwargs = {
                'engine': 'openpyxl',  # Generally faster for .xlsx files
                'sheet_name': sheet_name,
            }
            default_kwargs.update(kwargs)
            
            df = pd.read_excel(file_path, **default_kwargs)
            
            # Save to cache
            self._save_to_cache(file_path, df)
            
            load_time = time.time() - start_time
            self.logger.info(f"Loaded {file_path} from disk in {load_time:.2f}s "
                           f"({len(df)} rows, {len(df.columns)} columns)")
            
            return df
            
        except Exception as e:
            self.logger.error(f"Failed to load {file_path}: {e}")
            raise
    
    def load_multiple_excel(self, 
                           file_paths: Dict[str, str], 
                           max_workers: Optional[int] = None) -> Dict[str, pd.DataFrame]:
        """
        Load multiple Excel files in parallel.
        
        Args:
            file_paths: Dictionary mapping names to file paths
            max_workers: Maximum number of parallel workers
        
        Returns:
            Dictionary mapping names to DataFrames
        """
        if max_workers is None:
            max_workers = config.get('processing.parallel_workers', 4)
        
        start_time = time.time()
        results = {}
        
        # Use ThreadPoolExecutor for I/O bound operations
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all loading tasks
            future_to_name = {
                executor.submit(self.load_excel, path): name 
                for name, path in file_paths.items()
            }
            
            # Collect results
            for future in concurrent.futures.as_completed(future_to_name):
                name = future_to_name[future]
                try:
                    results[name] = future.result()
                except Exception as e:
                    self.logger.error(f"Failed to load {name}: {e}")
                    raise
        
        total_time = time.time() - start_time
        self.logger.info(f"Loaded {len(file_paths)} files in {total_time:.2f}s")
        
        return results
    
    def clear_cache(self, file_path: Optional[str] = None) -> None:
        """
        Clear cache files.
        
        Args:
            file_path: Specific file to clear cache for, or None to clear all
        """
        if file_path:
            cache_path = self._get_cache_path(file_path)
            cache_path.unlink(missing_ok=True)
            self.logger.info(f"Cleared cache for {file_path}")
        else:
            # Clear all cache files
            for cache_file in self.cache_dir.glob("*.pkl"):
                cache_file.unlink()
            self.logger.info("Cleared all cache files")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about cache usage."""
        if not self.cache_dir.exists():
            return {"cache_enabled": False}
        
        cache_files = list(self.cache_dir.glob("*.pkl"))
        total_size = sum(f.stat().st_size for f in cache_files)
        
        return {
            "cache_enabled": self.cache_enabled,
            "cache_dir": str(self.cache_dir),
            "cache_files": len(cache_files),
            "total_size_mb": total_size / (1024 * 1024),
            "files": [f.name for f in cache_files]
        }
