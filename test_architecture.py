#!/usr/bin/env python3
"""
Architecture Test Script

This script tests the new Empty Return Analysis architecture to ensure
all components work correctly together.
"""

import sys
import os
import tempfile
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from empty_return_analysis import DataProcessor, ConfigManager, Logger
    from empty_return_analysis.utils import ExcelLoader, DataValidator
    from empty_return_analysis.core import TimeCalculator
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Please ensure the empty_return_analysis package is properly installed.")
    sys.exit(1)


class ArchitectureTest:
    """Test suite for the new architecture."""
    
    def __init__(self):
        self.logger = Logger(__name__)
        self.test_results = []
        self.temp_dir = None
    
    def setup_test_environment(self):
        """Setup temporary test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.logger.info(f"Created test environment: {self.temp_dir}")
        
        # Create test data files
        self._create_test_data()
    
    def _create_test_data(self):
        """Create minimal test data files."""
        test_data = {
            'abnormal_supply': pd.DataFrame({
                'Task No.': ['T001', 'T002', 'T003'],
                'Task Type': ['常规供线', 'JIS供线', 'Inhouse Bin供线'],
                'Part': ['P001', 'P002', 'P003'],
                'Lineback Time': ['5/6/2025 10:20:02 AM', '5/6/2025 11:30:15 AM', '5/6/2025 12:45:30 AM'],
                'Last Lineback Time': ['5/6/2025 9:15:30 AM', '5/6/2025 10:25:45 AM', '5/6/2025 11:40:15 AM'],
                'Destination Area': ['W1001', 'SUMA001', 'LINE001'],
                'Create Time': ['5/6/2025 9:00:00 AM', '5/6/2025 10:00:00 AM', '5/6/2025 11:00:00 AM'],
                'Start Time': ['5/6/2025 9:05:00 AM', '5/6/2025 10:05:00 AM', '5/6/2025 11:05:00 AM'],
                'Finish Time': ['5/6/2025 9:10:00 AM', '5/6/2025 10:10:00 AM', '5/6/2025 11:10:00 AM'],
                'Agv Arrival Staging': ['5/6/2025 9:12:00 AM', '5/6/2025 10:12:00 AM', '5/6/2025 11:12:00 AM'],
                'AGV Staging LS Task Exe': ['5/6/2025 9:15:00 AM', '5/6/2025 10:15:00 AM', '5/6/2025 11:15:00 AM']
            }),
            
            'part_info': pd.DataFrame({
                'Part': ['P001', 'P002', 'P003'],
                'PN_EN': ['Part001_EN', 'Part002_EN', 'Part003_EN'],
                'PN_CN': ['零件001', '零件002', '零件003'],
                'FD': [2.0, 3.0, 1.5],
                'MSP': [100, 150, 80],
                'Car_Model_Summary': ['Model_A', 'Model_B', 'Model_A'],
                'Fitment_Point': ['Point1', 'Point2', 'Point3']
            }),
            
            'production_premise': pd.DataFrame({
                'Model': ['Model_A', 'Model_B'],
                'Frequency': [30, 25]  # cars per hour
            }),
            
            'time_config': pd.DataFrame({
                'Date': pd.date_range('2025-05-01', periods=10),
                'Start Time': [pd.Timestamp('08:00:00').time()] * 10,
                'End Time': [pd.Timestamp('17:00:00').time()] * 10,
                'Break Period 1 Start': [pd.Timestamp('12:00:00').time()] * 10,
                'Break Period 1 End': [pd.Timestamp('13:00:00').time()] * 10
            }),
            
            'auto_return_parts': pd.DataFrame({
                '零件号': ['P001', 'P003']
            }),
            
            'gm_management': pd.DataFrame({
                '当前零件': ['P001', 'P002', 'P003'],
                '业务类型': ['线边作业点', '线边作业点', '其他']
            }),
            
            'engine_job_info': pd.DataFrame({
                'biz_task_id': ['T001', 'T002', 'T003'],
                'last_last_lineback_time': ['5/6/2025 8:30:00 AM', '5/6/2025 9:45:00 AM', '5/6/2025 10:50:00 AM']
            })
        }
        
        # Save test data to temporary files
        for name, df in test_data.items():
            file_path = Path(self.temp_dir) / f"{name}.xlsx"
            df.to_excel(file_path, index=False)
            self.logger.info(f"Created test file: {file_path}")
    
    def test_component(self, test_name, test_func):
        """Run a single test component."""
        try:
            self.logger.info(f"Testing: {test_name}")
            test_func()
            self.test_results.append((test_name, True, None))
            print(f"✅ {test_name}")
        except Exception as e:
            self.test_results.append((test_name, False, str(e)))
            print(f"❌ {test_name}: {e}")
            self.logger.error(f"Test failed: {test_name} - {e}")
    
    def test_config_manager(self):
        """Test configuration management."""
        config = ConfigManager()
        
        # Test basic get/set operations
        config.set('test.value', 'test_data')
        assert config.get('test.value') == 'test_data'
        
        # Test nested keys
        config.set('test.nested.value', 42)
        assert config.get('test.nested.value') == 42
        
        # Test default values
        assert config.get('nonexistent.key', 'default') == 'default'
    
    def test_excel_loader(self):
        """Test Excel loading functionality."""
        loader = ExcelLoader(cache_enabled=False)  # Disable cache for testing
        
        # Test single file loading
        file_path = Path(self.temp_dir) / "part_info.xlsx"
        df = loader.load_excel(str(file_path))
        assert len(df) > 0
        assert 'Part' in df.columns
        
        # Test multiple file loading
        file_paths = {
            'part_info': str(Path(self.temp_dir) / "part_info.xlsx"),
            'time_config': str(Path(self.temp_dir) / "time_config.xlsx")
        }
        data = loader.load_multiple_excel(file_paths, max_workers=2)
        assert len(data) == 2
        assert 'part_info' in data
        assert 'time_config' in data
    
    def test_data_validator(self):
        """Test data validation functionality."""
        validator = DataValidator()
        
        # Load test data
        file_path = Path(self.temp_dir) / "part_info.xlsx"
        df = pd.read_excel(file_path)
        
        # Test basic validation
        result = validator.validate_dataframe(
            df, 
            'test_data',
            required_columns=['Part', 'PN_EN'],
            column_types={'Part': 'object'}
        )
        
        assert result['valid'] == True
        assert len(result['errors']) == 0
    
    def test_time_calculator(self):
        """Test time calculation functionality."""
        # Load calendar data
        calendar_file = Path(self.temp_dir) / "time_config.xlsx"
        calendar_df = pd.read_excel(calendar_file)
        
        calculator = TimeCalculator(calendar_df)
        
        # Test working hours retrieval
        start, end, breaks = calculator.get_working_hours_for_day('2025/05/01')
        assert start is not None
        assert end is not None
        
        # Test time difference calculation
        start_time = '5/1/2025 9:00:00 AM'
        end_time = '5/1/2025 10:00:00 AM'
        diff = calculator.calculate_time_difference(start_time, end_time)
        assert diff is not None
        assert diff > 0
    
    def test_data_processor_stage1(self):
        """Test stage 1 data processing."""
        # Update config to use test files
        from empty_return_analysis.config import config
        
        # Temporarily update file paths
        original_paths = {}
        test_files = {
            'abnormal_supply': str(Path(self.temp_dir) / "abnormal_supply.xlsx"),
            'part_info': str(Path(self.temp_dir) / "part_info.xlsx"),
            'production_premise': str(Path(self.temp_dir) / "production_premise.xlsx"),
            'time_config': str(Path(self.temp_dir) / "time_config.xlsx")
        }
        
        for key, path in test_files.items():
            original_paths[key] = config.get(f'data.input_files.{key}')
            config.set(f'data.input_files.{key}', path)
        
        try:
            processor = DataProcessor(cache_enabled=False)
            data = processor.load_all_data()
            
            # Test stage 1 processing
            result = processor.process_stage1(data)
            assert len(result) > 0
            assert 'Empty_Return_Gap' in result.columns
            assert '1Bin_Cover_Time' in result.columns
            
        finally:
            # Restore original paths
            for key, path in original_paths.items():
                config.set(f'data.input_files.{key}', path)
    
    def test_data_processor_integration(self):
        """Test full data processor integration."""
        # This is a simplified integration test
        from empty_return_analysis.config import config
        
        # Update all file paths for test
        test_files = {
            'abnormal_supply': str(Path(self.temp_dir) / "abnormal_supply.xlsx"),
            'part_info': str(Path(self.temp_dir) / "part_info.xlsx"),
            'production_premise': str(Path(self.temp_dir) / "production_premise.xlsx"),
            'time_config': str(Path(self.temp_dir) / "time_config.xlsx"),
            'auto_return_parts': str(Path(self.temp_dir) / "auto_return_parts.xlsx"),
            'gm_management': str(Path(self.temp_dir) / "gm_management.xlsx"),
            'engine_job_info': str(Path(self.temp_dir) / "engine_job_info.xlsx")
        }
        
        original_paths = {}
        for key, path in test_files.items():
            original_paths[key] = config.get(f'data.input_files.{key}')
            config.set(f'data.input_files.{key}', path)
        
        try:
            processor = DataProcessor(cache_enabled=False)
            
            # Test data loading
            data = processor.load_all_data()
            assert len(data) == 7  # Should load 7 files
            
            # Test stage 1
            stage1_result = processor.process_stage1(data)
            assert len(stage1_result) > 0
            
            # Test stage 2
            stage2_result = processor.process_stage2(stage1_result, data)
            assert len(stage2_result) > 0
            assert 'Task_Type_EN' in stage2_result.columns
            
            # Test stage 3
            final_result = processor.process_stage3(stage2_result)
            assert len(final_result) > 0
            assert 'last_last_vs_last_gap' in final_result.columns
            assert 'Staging_Time' in final_result.columns
            
        finally:
            # Restore original paths
            for key, path in original_paths.items():
                config.set(f'data.input_files.{key}', path)
    
    def run_all_tests(self):
        """Run all architecture tests."""
        print("🚀 Starting Empty Return Analysis Architecture Tests")
        print("=" * 60)
        
        self.setup_test_environment()
        
        # Run individual component tests
        self.test_component("Configuration Manager", self.test_config_manager)
        self.test_component("Excel Loader", self.test_excel_loader)
        self.test_component("Data Validator", self.test_data_validator)
        self.test_component("Time Calculator", self.test_time_calculator)
        self.test_component("Data Processor Stage 1", self.test_data_processor_stage1)
        self.test_component("Full Integration", self.test_data_processor_integration)
        
        # Print summary
        self.print_test_summary()
        
        # Cleanup
        self.cleanup()
    
    def print_test_summary(self):
        """Print test results summary."""
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for _, success, _ in self.test_results if success)
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {passed/total*100:.1f}%")
        
        if total - passed > 0:
            print("\nFAILED TESTS:")
            for name, success, error in self.test_results:
                if not success:
                    print(f"  ❌ {name}: {error}")
        
        print("=" * 60)
        
        if passed == total:
            print("🎉 All tests passed! The architecture is working correctly.")
            return True
        else:
            print("⚠️  Some tests failed. Please check the errors above.")
            return False
    
    def cleanup(self):
        """Clean up test environment."""
        if self.temp_dir:
            import shutil
            shutil.rmtree(self.temp_dir)
            self.logger.info(f"Cleaned up test environment: {self.temp_dir}")


def main():
    """Main test runner."""
    tester = ArchitectureTest()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ Architecture test completed successfully!")
        print("You can now run the main application with: python main_app.py")
        return 0
    else:
        print("\n❌ Architecture test failed!")
        print("Please fix the issues before using the application.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
