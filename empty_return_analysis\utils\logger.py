"""
Centralized logging utilities.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

from ..config import config


class Logger:
    """Centralized logger with configuration support."""
    
    _loggers = {}
    _configured = False
    
    def __init__(self, name: str):
        self.name = name
        self.logger = self._get_logger(name)
    
    @classmethod
    def _configure_logging(cls):
        """Configure logging system once."""
        if cls._configured:
            return
        
        # Get configuration
        log_level = config.get('logging.level', 'INFO')
        log_format = config.get('logging.format', 
                               '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        log_file = config.get('logging.file', 'empty_return_analysis.log')
        max_size = config.get('logging.max_size', '10MB')
        backup_count = config.get('logging.backup_count', 5)
        
        # Parse max_size
        if isinstance(max_size, str):
            if max_size.endswith('MB'):
                max_bytes = int(max_size[:-2]) * 1024 * 1024
            elif max_size.endswith('KB'):
                max_bytes = int(max_size[:-2]) * 1024
            else:
                max_bytes = int(max_size)
        else:
            max_bytes = max_size
        
        # Create formatter
        formatter = logging.Formatter(log_format)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # File handler with rotation
        if log_file:
            log_path = Path(log_file)
            log_path.parent.mkdir(exist_ok=True)
            
            file_handler = logging.handlers.RotatingFileHandler(
                log_file, 
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        
        cls._configured = True
    
    @classmethod
    def _get_logger(cls, name: str) -> logging.Logger:
        """Get or create logger instance."""
        if name not in cls._loggers:
            cls._configure_logging()
            cls._loggers[name] = logging.getLogger(name)
        
        return cls._loggers[name]
    
    def debug(self, message: str, *args, **kwargs):
        """Log debug message."""
        self.logger.debug(message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs):
        """Log info message."""
        self.logger.info(message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs):
        """Log warning message."""
        self.logger.warning(message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs):
        """Log error message."""
        self.logger.error(message, *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs):
        """Log critical message."""
        self.logger.critical(message, *args, **kwargs)
    
    def exception(self, message: str, *args, **kwargs):
        """Log exception with traceback."""
        self.logger.exception(message, *args, **kwargs)
