import pandas as pd
from datetime import datetime, timedelta

# ------------------ Step 1: 工作日信息读取 ------------------
def get_working_hours_for_day(date, calendar_df):
    date = pd.to_datetime(date, format='%Y/%m/%d')
    row = calendar_df[calendar_df['Date'] == date]

    if row.empty:
        raise ValueError(f"No working hours found for {date.strftime('%Y/%m/%d')}")

    row = row.iloc[0]
    start_time = row['Start Time']
    end_time = row['End Time']

    break_times = []
    for i in range(1, 5):
        b_start_col = f'Break Period {i} Start'
        b_end_col = f'Break Period {i} End'
        if pd.notna(row[b_start_col]) and pd.notna(row[b_end_col]):
            break_times.append((row[b_start_col], row[b_end_col]))

    return start_time, end_time, break_times

def calculate_time_difference(start, end, calendar_df):
    if start is None or end is None:
        return None

    if isinstance(start, str):
        start_date = datetime.strptime(start, '%m/%d/%Y %I:%M:%S %p')
    else:
        start_date = start

    if isinstance(end, str):
        end_date = datetime.strptime(end, '%m/%d/%Y %I:%M:%S %p')
    else:
        end_date = end

    def is_rest_day(work_start, work_end, breaks):
        work_start_nan = pd.isna(work_start) if isinstance(work_start, float) else work_start is None
        work_end_nan = pd.isna(work_end) if isinstance(work_end, float) else work_end is None
        return work_start_nan and work_end_nan and not breaks

    start_work_start, start_work_end, start_breaks = get_working_hours_for_day(start_date.strftime('%Y/%m/%d'), calendar_df)
    end_work_start, end_work_end, end_breaks = get_working_hours_for_day(end_date.strftime('%Y/%m/%d'), calendar_df)

    start_time = start_date.time()
    end_time = end_date.time()

    if is_rest_day(start_work_start, start_work_end, start_breaks):
        return None
    if is_rest_day(end_work_start, end_work_end, end_breaks):
        return None
    if not (start_work_start <= start_time <= start_work_end):
        return None
    if not (end_work_start <= end_time <= end_work_end):
        return None

    total_effective_time = 0
    current_date = start_date.date()

    while current_date <= end_date.date():
        work_start, work_end, break_times = get_working_hours_for_day(current_date.strftime('%Y/%m/%d'), calendar_df)

        if is_rest_day(work_start, work_end, break_times):
            current_date += timedelta(days=1)
            continue

        if current_date != start_date.date() and current_date != end_date.date():
            if not is_rest_day(work_start, work_end, break_times):
                return None

        day_start = max(start_date, datetime.combine(current_date, work_start)) if current_date == start_date.date() else datetime.combine(current_date, work_start)
        day_end = min(end_date, datetime.combine(current_date, work_end)) if current_date == end_date.date() else datetime.combine(current_date, work_end)

        for break_start, break_end in break_times:
            bs_dt = datetime.combine(current_date, break_start)
            be_dt = datetime.combine(current_date, break_end)
            if start_date >= bs_dt and end_date <= be_dt:
                return None
            if bs_dt <= start_date <= be_dt or bs_dt <= end_date <= be_dt:
                return None

        day_total_time = (day_end - day_start).seconds / 60
        rest_time = 0
        for break_start, break_end in break_times:
            bs_dt = datetime.combine(current_date, break_start)
            be_dt = datetime.combine(current_date, break_end)
            if day_start < bs_dt and day_end > be_dt:
                rest_time += (be_dt - bs_dt).seconds / 60

        total_effective_time += day_total_time - rest_time
        current_date += timedelta(days=1)

    return round(total_effective_time, 2)

# ------------------ Step 2: 主处理逻辑 ------------------
def main():
    # 读取数据
    task_df = pd.read_excel('Abnormal supply.xlsx') # 数据原表
    part_info_df = pd.read_excel('Part Info.xlsx') # Part Info
    production_df = pd.read_excel('Production premise.xlsx') # Production premise
    calendar_df = pd.read_excel('time_config.xlsx')  # 包含工作时间和休息时间定义

    # 数据清洗
    task_df = task_df[task_df['Task Type'].isin(['常规供线', 'JIS供线', 'Inhouse Bin供线'])]
    task_df = task_df.dropna(subset=['Lineback Time', 'Last Lineback Time'])

    # 计算 Empty_Return_Gap
    def compute_gap(row):
        try:
            return calculate_time_difference(row['Last Lineback Time'], row['Lineback Time'], calendar_df)
        except:
            return None

    task_df['Empty_Return_Gap'] = task_df.apply(compute_gap, axis=1)

    # 合并零件信息
    part_info_columns = ['Part', 'PN_EN', 'PN_CN', 'FD', 'MSP', 'Car_Model_Summary', 'Fitment_Point']
    task_df = task_df.merge(part_info_df[part_info_columns], on='Part', how='left')

    # 生产频率映射
    prod_freq_map = dict(zip(production_df.iloc[:, 0], production_df.iloc[:, 1]))

    def compute_1bin_cover(row):
        try:
            model = row['Car_Model_Summary']
            fd = row['FD']
            freq = prod_freq_map.get(model, None)
            if freq and freq > 0:
                return round((60 / freq) * fd, 2)
        except:
            return None

    task_df['1Bin_Cover_Time'] = task_df.apply(compute_1bin_cover, axis=1)
    task_df['2Bins_Cover_Time'] = task_df['1Bin_Cover_Time'] * 2

    # ---------- ✅ 时间字段格式化为 '5/6/2025 10:20:02 AM' 格式 ----------
    time_columns = ['Create Time', 'Lineback Time', 'Last Lineback Time', 'Start Time', 'Finish Time', 'Agv Arrival Staging', 'AGV Staging LS Task Exe']
    for col in time_columns:
        if col in task_df.columns:
            task_df[col] = pd.to_datetime(task_df[col]).dt.strftime('%m/%d/%Y %I:%M:%S %p')

    # ------------------ 重新排列并导出结果 ------------------
    output_columns = [
        'Task No.', 'Task Type', 'status', 'Task Status', 'Part', 'Rack No.',
        'Source Location', 'Start Area', 'Destination Area', 'Create Time',
        'Start Time', 'Finish Time', 'Agv Arrival Staging', 'AGV Staging LS Task Exe',  # 插入新列
        'Lineback Time', 'Last Lineback Time', 'Empty_Return_Gap',
        'PN_EN', 'PN_CN', 'FD', 'MSP', 'Car_Model_Summary',
        '1Bin_Cover_Time', '2Bins_Cover_Time', 'Fitment_Point'
    ]
    output_df = task_df[output_columns]
    output_df.to_excel('Empty Return Analysis.xlsx', index=False)
    print("处理完成，结果保存在：Empty Return Analysis.xlsx")

# ------------------ 入口 ------------------
if __name__ == '__main__':
    main()
