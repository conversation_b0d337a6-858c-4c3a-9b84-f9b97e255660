import os
import re
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from scipy import stats
import matplotlib.gridspec as gridspec
from matplotlib.backends.backend_pdf import PdfPages

def load_data():
    """加载原始数据文件"""
    try:
        main_df = pd.read_excel('Empty Return Analysis.xlsx')
        auto_return_parts = pd.read_excel('5X线自动返空零件清单.xlsx')
        gm_df = pd.read_excel('地码管理.xlsx')
        engine_job_df = pd.read_excel('engine_job_info_2025-0701-0714.xlsx')
        
        print("数据加载成功")
        return main_df, auto_return_parts, gm_df, engine_job_df
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        raise
    except Exception as e:
        print(f"数据加载出错: {e}")
        raise

    
def translate_task_types(df):
    """添加任务类型的英文翻译列"""
    df['Task_Type_EN'] = df['Task Type'].map({
        '常规供线': 'Normal Supply',
        'JIS供线': 'JIS Supply',
        'Inhouse Bin供线': 'Inhouse Bin Supply'
    })
    return df

def translate_parts(df):
    """添加零件的英文翻译列"""
    part_translation = {
        '左后门板G48': 'LH Rear Door Panel G48',
        '右后门板G48': 'RH Rear Door Panel G48',
        '右后门板G28': 'RH Rear Door Panel G28',
        '左后门板G28': 'LH Rear Door Panel G28',
        'G48左前门板': 'LH Front Door Panel G48',
        'G48右前门板': 'RH Front Door Panel G48',
        'G28左前门板': 'LH Front Door Panel G28',
        'G28右前门板': 'RH Front Door Panel G28',
        '顶饰条L': 'Strip Roof LH',
        '顶饰条R': 'Strip Roof RH',
        '主线束G28': 'Main Harness G28',
        '主线束G48': 'Main Harness G48',
        '中控台G28': 'Center Console G28',
        '中控台G48': 'Center Console G48',
        '上扰流板G48': 'Spoiler Trunk Lid G48',
        'G48座椅侧翼右': 'Bolster RH G48',
        'G48座椅侧翼左': 'Bolster LH G48',
        'G48方向盘': 'Steering Wheel G48',
        '仪表板G28': 'IP G28',
        '仪表板G48': 'IP G48',
        '车外后视镜_副驾G48': 'Out Mirror Driver G48',
        '车外后视镜_主驾G48': 'Out Mirror Passenger G48',
        '仪表板线束 G28': 'IP Harness G28',
        '双肾格栅G28BEV': 'Kidney Grille G28BEV'
    }
    
    # 根据条件转换Part列的值
    df['Part_EN'] = df.apply(
        lambda x: part_translation.get(x['Part'], x['Part']) 
        if x['Task Type'] == 'JIS供线' else x['Part'], 
        axis=1
    )
    return df

def determine_destination(df):
    """确定目的地类型并添加Destination列"""
    def get_destination(row):
        if row['Task Type'] == 'Inhouse Bin供线':
            return 'LINE'
        elif row['Task Type'] == 'JIS供线':
            if row['Destination Area'].startswith('LINE'):
                return 'LINE'
            elif row['Destination Area'].startswith('SUMA'):
                return 'SUMA'
        elif row['Task Type'] == '常规供线':
            if len(row['Destination Area']) >= 5 and row['Destination Area'][4] in ('K', 'S'):
                return 'SUMA'
            elif len(row['Destination Area']) >= 5 and row['Destination Area'][4] in ('U', 'F'):
                return 'TU'
            elif row['Destination Area'].startswith(('TU', 'WTU')):
                return 'TU'
            elif row['Destination Area'].startswith('W') and len(row['Destination Area']) >= 2 and row['Destination Area'][1].isdigit():
                return 'LINE'
        return 'UNKNOWN'
    
    df['Destination'] = df.apply(get_destination, axis=1)
    return df

def add_automatic_return_info(df, auto_return_parts):
    """添加自动返空信息列"""
    automatic_return_part_list = auto_return_parts['零件号'].tolist()
    df['Automatic_Empty_Return'] = df['Part'].apply(
        lambda x: 'Automatic Return' if x in automatic_return_part_list else 'Non-Automatic Return'
    )
    return df

def process_quality_control_data(gm_df, filtered_df):
    """处理质量控制数据并合并"""
    # 处理地码管理数据
    gm_df['PN_WO_AI'] = gm_df['当前零件'].apply(
        lambda x: x[:7] if isinstance(x, str) and not re.search('[\u4e00-\u9fa5]', x) else x
    )
    
    # 统计线边作业点的数量
    qr_count = gm_df[gm_df['业务类型'] == '线边作业点'].groupby('PN_WO_AI').size().reset_index(name='QR_Qty')
    
    # 处理零件编号
    filtered_df['PN_WO_AI'] = filtered_df['Part'].apply(
        lambda x: x[:7] if not re.search('[\u4e00-\u9fa5]', x) else x
    )
    
    # 合并数据
    filtered_df = filtered_df.merge(qr_count, on='PN_WO_AI', how='left')
    
    # 填充缺失值并转换类型
    filtered_df['QR_Qty'] = filtered_df['QR_Qty'].fillna(0).astype(int)
    
    # 特殊情况处理
    filtered_df.loc[(filtered_df['Part'] == '5B36BF9-01'), 'QR_Qty'] = 2
    filtered_df.loc[
        (filtered_df['Part_EN'] == 'Main Harness G28') | 
        (filtered_df['Part_EN'] == 'Main Harness G48'), 
        'QR_Qty'
    ] = 2
    
    return filtered_df

def calculate_cv(filtered_df):
    """计算变异系数(CV)并合并到数据中"""
    # 计算每个零件的标准差和均值
    cv_df = filtered_df.groupby('Part_EN')['Empty_Return_Gap'].agg(['std', 'mean']).reset_index()
    
    # 过滤掉均值为零的行，避免除零错误
    cv_df = cv_df[cv_df['mean'] != 0]
    
    # 计算变异系数(CV)
    cv_df['CV'] = (cv_df['std'] / cv_df['mean']) * 100
    
    # 合并回主数据框
    return filtered_df.merge(cv_df[['Part_EN', 'CV']], on='Part_EN', how='left')

def merge_engine_job_data(filtered_df, engine_job_df):
    """合并引擎作业数据"""
    # 合并数据以添加last_last_lineback_time列
    filtered_df = filtered_df.merge(
        engine_job_df[['biz_task_id', 'last_last_lineback_time']], 
        left_on='Task_No.', 
        right_on='biz_task_id', 
        how='left'
    )
    
    # 转换时间格式
    filtered_df['last_last_lineback_time'] = pd.to_datetime(
        filtered_df['last_last_lineback_time']
    ).dt.strftime('%m/%d/%Y %I:%M:%S %p')
    
    # 删除冗余列
    return filtered_df.drop(columns=['biz_task_id'], errors='ignore')

def main():
  
    # 加载数据
    main_df, auto_return_parts, gm_df, engine_job_df = load_data()
    
    # 数据转换与处理
    df = main_df.copy()
    df = translate_task_types(df)
    df = translate_parts(df)
    df = determine_destination(df)
    df = add_automatic_return_info(df, auto_return_parts)
    
    # 替换列名中的空格为下划线
    df.columns = df.columns.str.replace(' ', '_')
    
    # 数据过滤
    filtered_df = df[df['Empty_Return_Gap'].notna()]
    filtered_df = filtered_df[filtered_df['Empty_Return_Gap'] <= 3 * filtered_df['1Bin_Cover_Time']]
    
    # 计算变异系数
    filtered_df = calculate_cv(filtered_df)
    
    # 处理质量控制数据
    filtered_df = process_quality_control_data(gm_df, filtered_df)
    
    # 计算3Bins覆盖时间
    filtered_df['3Bins_Cover_Time'] = filtered_df['1Bin_Cover_Time'] * 3
    
    # 合并引擎作业数据
    filtered_df = merge_engine_job_data(filtered_df, engine_job_df)
    
    # 保存处理结果
    try:
        filtered_df.to_excel('Empty Return Analysis Update.xlsx', index=False)
        print("处理完成，结果已保存至 'Empty Return Analysis Update.xlsx'")
    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == "__main__":
    main()
