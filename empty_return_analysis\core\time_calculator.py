"""
Optimized time calculation utilities.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
from typing import Optional, List, Tuple, Dict, Any
from functools import lru_cache
import concurrent.futures

from ..utils.logger import Logger
from ..utils.cache import cached
from ..config import config


class TimeCalculator:
    """Optimized time calculation with caching and vectorization."""
    
    def __init__(self, calendar_df: pd.DataFrame):
        self.logger = Logger(__name__)
        self.calendar_df = calendar_df.copy()
        self._prepare_calendar_data()
    
    def _prepare_calendar_data(self) -> None:
        """Prepare and optimize calendar data for faster lookups."""
        # Convert Date column to datetime if not already
        if not pd.api.types.is_datetime64_any_dtype(self.calendar_df['Date']):
            self.calendar_df['Date'] = pd.to_datetime(self.calendar_df['Date'])
        
        # Set Date as index for faster lookups
        self.calendar_df.set_index('Date', inplace=True)
        
        # Pre-process break times
        self._process_break_times()
        
        self.logger.info(f"Calendar data prepared with {len(self.calendar_df)} days")
    
    def _process_break_times(self) -> None:
        """Pre-process break times for faster access."""
        break_columns = []
        for i in range(1, 5):
            start_col = f'Break Period {i} Start'
            end_col = f'Break Period {i} End'
            if start_col in self.calendar_df.columns and end_col in self.calendar_df.columns:
                break_columns.append((start_col, end_col))
        
        # Create a consolidated break times column
        def get_breaks(row):
            breaks = []
            for start_col, end_col in break_columns:
                if pd.notna(row[start_col]) and pd.notna(row[end_col]):
                    breaks.append((row[start_col], row[end_col]))
            return breaks
        
        self.calendar_df['break_times'] = self.calendar_df.apply(get_breaks, axis=1)
    
    @lru_cache(maxsize=1000)
    def get_working_hours_for_day(self, date_str: str) -> Tuple[Optional[time], Optional[time], List[Tuple[time, time]]]:
        """
        Get working hours for a specific day with caching.
        
        Args:
            date_str: Date string in 'YYYY/MM/DD' format
        
        Returns:
            Tuple of (start_time, end_time, break_times)
        """
        try:
            date = pd.to_datetime(date_str, format='%Y/%m/%d')
            
            if date not in self.calendar_df.index:
                raise ValueError(f"No working hours found for {date_str}")
            
            row = self.calendar_df.loc[date]
            
            start_time = row['Start Time'] if pd.notna(row['Start Time']) else None
            end_time = row['End Time'] if pd.notna(row['End Time']) else None
            break_times = row['break_times']
            
            return start_time, end_time, break_times
            
        except Exception as e:
            self.logger.error(f"Error getting working hours for {date_str}: {e}")
            raise
    
    @staticmethod
    def is_rest_day(work_start: Optional[time], work_end: Optional[time], breaks: List) -> bool:
        """Check if a day is a rest day."""
        work_start_nan = work_start is None or pd.isna(work_start)
        work_end_nan = work_end is None or pd.isna(work_end)
        return work_start_nan and work_end_nan and not breaks
    
    def calculate_time_difference(self, start: Any, end: Any) -> Optional[float]:
        """
        Calculate time difference between two timestamps considering working hours.
        
        Args:
            start: Start timestamp
            end: End timestamp
        
        Returns:
            Time difference in minutes, or None if invalid
        """
        if start is None or end is None or pd.isna(start) or pd.isna(end):
            return None
        
        try:
            # Parse timestamps
            if isinstance(start, str):
                start_date = datetime.strptime(start, config.get('processing.time_format', '%m/%d/%Y %I:%M:%S %p'))
            else:
                start_date = pd.to_datetime(start)
            
            if isinstance(end, str):
                end_date = datetime.strptime(end, config.get('processing.time_format', '%m/%d/%Y %I:%M:%S %p'))
            else:
                end_date = pd.to_datetime(end)
            
            # Ensure start is before end
            if start_date >= end_date:
                return None
            
            return self._calculate_working_time_difference(start_date, end_date)
            
        except Exception as e:
            self.logger.debug(f"Error calculating time difference: {e}")
            return None
    
    def _calculate_working_time_difference(self, start_date: datetime, end_date: datetime) -> Optional[float]:
        """Calculate working time difference between two datetime objects."""
        try:
            # Get working hours for start and end dates
            start_work_start, start_work_end, start_breaks = self.get_working_hours_for_day(
                start_date.strftime('%Y/%m/%d')
            )
            end_work_start, end_work_end, end_breaks = self.get_working_hours_for_day(
                end_date.strftime('%Y/%m/%d')
            )
            
            start_time = start_date.time()
            end_time = end_date.time()
            
            # Check if dates are rest days
            if self.is_rest_day(start_work_start, start_work_end, start_breaks):
                return None
            if self.is_rest_day(end_work_start, end_work_end, end_breaks):
                return None
            
            # Check if times are within working hours
            if not (start_work_start <= start_time <= start_work_end):
                return None
            if not (end_work_start <= end_time <= end_work_end):
                return None
            
            # Calculate effective working time
            total_effective_time = 0
            current_date = start_date.date()
            
            while current_date <= end_date.date():
                work_start, work_end, break_times = self.get_working_hours_for_day(
                    current_date.strftime('%Y/%m/%d')
                )
                
                if self.is_rest_day(work_start, work_end, break_times):
                    current_date += timedelta(days=1)
                    continue
                
                # For multi-day spans, only allow same-day calculations
                if current_date != start_date.date() and current_date != end_date.date():
                    if not self.is_rest_day(work_start, work_end, break_times):
                        return None
                
                # Calculate day boundaries
                day_start = max(start_date, datetime.combine(current_date, work_start)) \
                    if current_date == start_date.date() else datetime.combine(current_date, work_start)
                day_end = min(end_date, datetime.combine(current_date, work_end)) \
                    if current_date == end_date.date() else datetime.combine(current_date, work_end)
                
                # Check for break time conflicts
                for break_start, break_end in break_times:
                    bs_dt = datetime.combine(current_date, break_start)
                    be_dt = datetime.combine(current_date, break_end)
                    
                    # If the entire period is within a break, return None
                    if start_date >= bs_dt and end_date <= be_dt:
                        return None
                    
                    # If period overlaps with break, return None
                    if (bs_dt <= start_date <= be_dt) or (bs_dt <= end_date <= be_dt):
                        return None
                
                # Calculate working time for this day
                day_total_time = (day_end - day_start).total_seconds() / 60
                
                # Subtract break times that fall within the working period
                rest_time = 0
                for break_start, break_end in break_times:
                    bs_dt = datetime.combine(current_date, break_start)
                    be_dt = datetime.combine(current_date, break_end)
                    
                    if day_start < bs_dt and day_end > be_dt:
                        rest_time += (be_dt - bs_dt).total_seconds() / 60
                
                total_effective_time += day_total_time - rest_time
                current_date += timedelta(days=1)
            
            return round(total_effective_time, 2)
            
        except Exception as e:
            self.logger.debug(f"Error in working time calculation: {e}")
            return None
    
    def calculate_time_differences_vectorized(self, 
                                            df: pd.DataFrame, 
                                            start_col: str, 
                                            end_col: str,
                                            result_col: str = 'time_diff',
                                            chunk_size: Optional[int] = None) -> pd.DataFrame:
        """
        Calculate time differences for entire DataFrame using vectorized operations.
        
        Args:
            df: DataFrame containing time columns
            start_col: Name of start time column
            end_col: Name of end time column
            result_col: Name of result column
            chunk_size: Size of chunks for processing
        
        Returns:
            DataFrame with time differences calculated
        """
        if chunk_size is None:
            chunk_size = config.get('processing.chunk_size', 10000)
        
        self.logger.info(f"Calculating time differences for {len(df)} records")
        
        # Process in chunks for better memory management
        results = []
        
        for i in range(0, len(df), chunk_size):
            chunk = df.iloc[i:i+chunk_size].copy()
            
            # Apply calculation to chunk
            chunk[result_col] = chunk.apply(
                lambda row: self.calculate_time_difference(row[start_col], row[end_col]),
                axis=1
            )
            
            results.append(chunk)
            
            if i % (chunk_size * 10) == 0:  # Log progress every 10 chunks
                self.logger.info(f"Processed {min(i + chunk_size, len(df))}/{len(df)} records")
        
        result_df = pd.concat(results, ignore_index=True)
        
        # Log statistics
        valid_count = result_df[result_col].notna().sum()
        self.logger.info(f"Calculated {valid_count}/{len(df)} valid time differences")
        
        return result_df
    
    def calculate_parallel(self, 
                          df: pd.DataFrame, 
                          start_col: str, 
                          end_col: str,
                          max_workers: Optional[int] = None) -> pd.Series:
        """
        Calculate time differences using parallel processing.
        
        Args:
            df: DataFrame containing time columns
            start_col: Name of start time column
            end_col: Name of end time column
            max_workers: Maximum number of worker threads
        
        Returns:
            Series with calculated time differences
        """
        if max_workers is None:
            max_workers = config.get('processing.parallel_workers', 4)
        
        chunk_size = len(df) // max_workers
        if chunk_size == 0:
            chunk_size = len(df)
        
        chunks = [df.iloc[i:i+chunk_size] for i in range(0, len(df), chunk_size)]
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [
                executor.submit(self._process_chunk, chunk, start_col, end_col)
                for chunk in chunks
            ]
            
            results = []
            for future in concurrent.futures.as_completed(futures):
                results.append(future.result())
        
        return pd.concat(results, ignore_index=True)
    
    def _process_chunk(self, chunk: pd.DataFrame, start_col: str, end_col: str) -> pd.Series:
        """Process a chunk of data for parallel calculation."""
        return chunk.apply(
            lambda row: self.calculate_time_difference(row[start_col], row[end_col]),
            axis=1
        )

    def clear_cache(self) -> None:
        """Clear the LRU cache for working hours."""
        self.get_working_hours_for_day.cache_clear()
        self.logger.info("Time calculator cache cleared")
