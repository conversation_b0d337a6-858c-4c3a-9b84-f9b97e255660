"""
Data validation utilities.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from .logger import Logger


class DataValidator:
    """Data validation and quality checks."""
    
    def __init__(self):
        self.logger = Logger(__name__)
        self.validation_results = []
    
    def validate_dataframe(self, 
                          df: pd.DataFrame, 
                          name: str,
                          required_columns: Optional[List[str]] = None,
                          column_types: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Validate DataFrame structure and content.
        
        Args:
            df: DataFrame to validate
            name: Name for logging purposes
            required_columns: List of required column names
            column_types: Expected column types
        
        Returns:
            Validation results dictionary
        """
        results = {
            'name': name,
            'valid': True,
            'errors': [],
            'warnings': [],
            'stats': {
                'rows': len(df),
                'columns': len(df.columns),
                'memory_usage_mb': df.memory_usage(deep=True).sum() / (1024 * 1024),
                'null_counts': df.isnull().sum().to_dict()
            }
        }
        
        # Check required columns
        if required_columns:
            missing_columns = set(required_columns) - set(df.columns)
            if missing_columns:
                error_msg = f"Missing required columns: {missing_columns}"
                results['errors'].append(error_msg)
                results['valid'] = False
        
        # Check column types
        if column_types:
            for col, expected_type in column_types.items():
                if col in df.columns:
                    actual_type = str(df[col].dtype)
                    if not self._is_compatible_type(actual_type, expected_type):
                        warning_msg = f"Column '{col}' has type '{actual_type}', expected '{expected_type}'"
                        results['warnings'].append(warning_msg)
        
        # Check for completely empty columns
        empty_columns = df.columns[df.isnull().all()].tolist()
        if empty_columns:
            warning_msg = f"Completely empty columns: {empty_columns}"
            results['warnings'].append(warning_msg)
        
        # Check for duplicate rows
        duplicate_count = df.duplicated().sum()
        if duplicate_count > 0:
            warning_msg = f"Found {duplicate_count} duplicate rows"
            results['warnings'].append(warning_msg)
        
        # Log results
        if results['errors']:
            self.logger.error(f"Validation failed for {name}: {results['errors']}")
        elif results['warnings']:
            self.logger.warning(f"Validation warnings for {name}: {results['warnings']}")
        else:
            self.logger.info(f"Validation passed for {name}")
        
        self.validation_results.append(results)
        return results
    
    def _is_compatible_type(self, actual: str, expected: str) -> bool:
        """Check if actual type is compatible with expected type."""
        type_mappings = {
            'object': ['object', 'string'],
            'int64': ['int64', 'int32', 'integer'],
            'float64': ['float64', 'float32', 'float'],
            'datetime64[ns]': ['datetime64[ns]', 'datetime'],
            'bool': ['bool', 'boolean']
        }
        
        return expected in type_mappings.get(actual, [actual])
    
    def validate_time_columns(self, 
                             df: pd.DataFrame, 
                             time_columns: List[str],
                             time_format: str = '%m/%d/%Y %I:%M:%S %p') -> Dict[str, Any]:
        """
        Validate time columns format and values.
        
        Args:
            df: DataFrame containing time columns
            time_columns: List of time column names
            time_format: Expected time format
        
        Returns:
            Validation results
        """
        results = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'parsed_counts': {}
        }
        
        for col in time_columns:
            if col not in df.columns:
                continue
            
            # Count valid time values
            valid_count = 0
            invalid_count = 0
            
            for value in df[col].dropna():
                try:
                    if isinstance(value, str):
                        datetime.strptime(value, time_format)
                    valid_count += 1
                except (ValueError, TypeError):
                    invalid_count += 1
            
            results['parsed_counts'][col] = {
                'valid': valid_count,
                'invalid': invalid_count,
                'null': df[col].isnull().sum()
            }
            
            if invalid_count > 0:
                warning_msg = f"Column '{col}' has {invalid_count} invalid time values"
                results['warnings'].append(warning_msg)
        
        return results
    
    def validate_business_rules(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate business-specific rules.
        
        Args:
            df: DataFrame to validate
        
        Returns:
            Validation results
        """
        results = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'rule_checks': {}
        }
        
        # Rule 1: Empty_Return_Gap should be positive
        if 'Empty_Return_Gap' in df.columns:
            negative_gaps = (df['Empty_Return_Gap'] < 0).sum()
            results['rule_checks']['negative_gaps'] = negative_gaps
            if negative_gaps > 0:
                warning_msg = f"Found {negative_gaps} negative Empty_Return_Gap values"
                results['warnings'].append(warning_msg)
        
        # Rule 2: Task Type should be in allowed values
        if 'Task Type' in df.columns:
            allowed_types = ['常规供线', 'JIS供线', 'Inhouse Bin供线']
            invalid_types = ~df['Task Type'].isin(allowed_types)
            invalid_count = invalid_types.sum()
            results['rule_checks']['invalid_task_types'] = invalid_count
            if invalid_count > 0:
                warning_msg = f"Found {invalid_count} invalid Task Type values"
                results['warnings'].append(warning_msg)
        
        # Rule 3: Lineback Time should be after Last Lineback Time
        if all(col in df.columns for col in ['Lineback Time', 'Last Lineback Time']):
            try:
                lineback_time = pd.to_datetime(df['Lineback Time'])
                last_lineback_time = pd.to_datetime(df['Last Lineback Time'])
                invalid_order = (lineback_time <= last_lineback_time).sum()
                results['rule_checks']['invalid_time_order'] = invalid_order
                if invalid_order > 0:
                    warning_msg = f"Found {invalid_order} records where Lineback Time <= Last Lineback Time"
                    results['warnings'].append(warning_msg)
            except Exception as e:
                error_msg = f"Failed to validate time order: {e}"
                results['errors'].append(error_msg)
                results['valid'] = False
        
        return results
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """Get summary of all validation results."""
        total_validations = len(self.validation_results)
        failed_validations = sum(1 for r in self.validation_results if not r['valid'])
        
        return {
            'total_validations': total_validations,
            'failed_validations': failed_validations,
            'success_rate': (total_validations - failed_validations) / total_validations if total_validations > 0 else 0,
            'results': self.validation_results
        }
    
    def clear_results(self) -> None:
        """Clear validation results."""
        self.validation_results.clear()
