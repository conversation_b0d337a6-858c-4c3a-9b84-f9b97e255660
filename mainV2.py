import pandas as pd
from datetime import datetime, timedelta

# 读取数据
df = pd.read_excel('Empty Return Analysis Update.xlsx')  # 修改为你的原始文件名
calendar_df = pd.read_excel('time_config.xlsx')

# 获取工作日配置
def get_working_hours_for_day(date, calendar_df):
    date = pd.to_datetime(date, format='%Y/%m/%d')
    row = calendar_df[calendar_df['Date'] == date]

    if row.empty:
        raise ValueError(f"No working hours found for {date.strftime('%Y/%m/%d')}")

    row = row.iloc[0]
    start_time = row['Start Time']
    end_time = row['End Time']

    break_times = []
    for i in range(1, 5):
        b_start_col = f'Break Period {i} Start'
        b_end_col = f'Break Period {i} End'
        if pd.notna(row[b_start_col]) and pd.notna(row[b_end_col]):
            break_times.append((row[b_start_col], row[b_end_col]))

    return start_time, end_time, break_times

# 判断是否为休息日
def is_rest_day(work_start, work_end, breaks):
    work_start_nan = pd.isna(work_start) if isinstance(work_start, float) else work_start is None
    work_end_nan = pd.isna(work_end) if isinstance(work_end, float) else work_end is None
    return work_start_nan and work_end_nan and not breaks

# 核心计算函数
def calculate_time_difference(start, end, calendar_df):
    if pd.isna(start) or pd.isna(end):
        return None
    if isinstance(start, str):
        start_date = datetime.strptime(start, '%m/%d/%Y %I:%M:%S %p')
    else:
        start_date = pd.to_datetime(start)

    if isinstance(end, str):
        end_date = datetime.strptime(end, '%m/%d/%Y %I:%M:%S %p')
    else:
        end_date = pd.to_datetime(end)

    start_work_start, start_work_end, start_breaks = get_working_hours_for_day(start_date.strftime('%Y/%m/%d'), calendar_df)
    end_work_start, end_work_end, end_breaks = get_working_hours_for_day(end_date.strftime('%Y/%m/%d'), calendar_df)

    start_time = start_date.time()
    end_time = end_date.time()

    if is_rest_day(start_work_start, start_work_end, start_breaks):
        return None
    if is_rest_day(end_work_start, end_work_end, end_breaks):
        return None
    if not (start_work_start <= start_time <= start_work_end):
        return None
    if not (end_work_start <= end_time <= end_work_end):
        return None

    total_effective_time = 0
    current_date = start_date.date()

    while current_date <= end_date.date():
        work_start, work_end, break_times = get_working_hours_for_day(current_date.strftime('%Y/%m/%d'), calendar_df)

        if is_rest_day(work_start, work_end, break_times):
            current_date += timedelta(days=1)
            continue

        if current_date != start_date.date() and current_date != end_date.date():
            if not is_rest_day(work_start, work_end, break_times):
                return None

        day_start = max(start_date, datetime.combine(current_date, work_start)) if current_date == start_date.date() else datetime.combine(current_date, work_start)
        day_end = min(end_date, datetime.combine(current_date, work_end)) if current_date == end_date.date() else datetime.combine(current_date, work_end)

        for break_start, break_end in break_times:
            bs_dt = datetime.combine(current_date, break_start)
            be_dt = datetime.combine(current_date, break_end)
            if start_date >= bs_dt and end_date <= be_dt:
                return None
            if bs_dt <= start_date <= be_dt or bs_dt <= end_date <= be_dt:
                return None

        day_total_time = (day_end - day_start).seconds / 60
        rest_time = 0
        for break_start, break_end in break_times:
            bs_dt = datetime.combine(current_date, break_start)
            be_dt = datetime.combine(current_date, break_end)
            if day_start < bs_dt and day_end > be_dt:
                rest_time += (be_dt - bs_dt).seconds / 60

        total_effective_time += day_total_time - rest_time
        current_date += timedelta(days=1)

    return round(total_effective_time, 2)

# 添加新列 - last_last_vs_last_gap（AH - AI）
df['last_last_vs_last_gap'] = df.apply(lambda row: 0 if pd.isna(row['last_last_lineback_time'])
                                       else calculate_time_difference(row['last_last_lineback_time'], row['Last_Lineback_Time'], calendar_df), axis=1)

# 添加新列 - Staging_Time（M - N）
df['Staging_Time'] = df.apply(lambda row: 0 if pd.isna(row['AGV_Staging_LS_Task_Exe'])
                              else calculate_time_difference(row['Agv_Arrival_Staging'], row['AGV_Staging_LS_Task_Exe'], calendar_df), axis=1)

# 保存文件
df.to_excel('result.xlsx', index=False)