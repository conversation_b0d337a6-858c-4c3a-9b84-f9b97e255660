#!/usr/bin/env python3
"""
Empty Return Analysis - Main Application Entry Point

A comprehensive data processing pipeline for analyzing empty return patterns
in manufacturing supply chain operations.

Usage:
    python main_app.py [options]

Options:
    --config PATH       Path to configuration file (default: config.yaml)
    --stage STAGE       Run specific stage only (1, 2, 3, or all)
    --no-cache          Disable caching
    --clear-cache       Clear all caches before running
    --validate-only     Only validate data without processing
    --output PATH       Override output file path
    --log-level LEVEL   Set logging level (DEBUG, INFO, WARNING, ERROR)
    --parallel          Enable parallel processing
    --help              Show this help message

Examples:
    python main_app.py                          # Run full pipeline
    python main_app.py --stage 1               # Run only stage 1
    python main_app.py --no-cache              # Run without caching
    python main_app.py --clear-cache           # Clear cache and run
    python main_app.py --validate-only         # Only validate data
    python main_app.py --config custom.yaml    # Use custom config
"""

import argparse
import sys
import time
from pathlib import Path
from typing import Optional

from empty_return_analysis import DataProcessor, Config<PERSON>anager, Logger
from empty_return_analysis.config import config


def parse_arguments() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Empty Return Analysis - Data Processing Pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__.split('Usage:')[1] if 'Usage:' in __doc__ else ""
    )
    
    parser.add_argument(
        '--config', 
        type=str, 
        default='config.yaml',
        help='Path to configuration file (default: config.yaml)'
    )
    
    parser.add_argument(
        '--stage', 
        type=str, 
        choices=['1', '2', '3', 'all'],
        default='all',
        help='Run specific stage only (1, 2, 3, or all)'
    )
    
    parser.add_argument(
        '--no-cache', 
        action='store_true',
        help='Disable caching'
    )
    
    parser.add_argument(
        '--clear-cache', 
        action='store_true',
        help='Clear all caches before running'
    )
    
    parser.add_argument(
        '--validate-only', 
        action='store_true',
        help='Only validate data without processing'
    )
    
    parser.add_argument(
        '--output', 
        type=str,
        help='Override output file path'
    )
    
    parser.add_argument(
        '--log-level', 
        type=str,
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='Set logging level'
    )
    
    parser.add_argument(
        '--parallel', 
        action='store_true',
        help='Enable parallel processing'
    )
    
    parser.add_argument(
        '--help-full', 
        action='store_true',
        help='Show full help with examples'
    )
    
    return parser.parse_args()


def setup_configuration(args: argparse.Namespace) -> None:
    """Setup configuration based on command line arguments."""
    # Load custom config file if specified
    if args.config != 'config.yaml':
        global config
        config = ConfigManager(args.config)
    
    # Override configuration with command line arguments
    if args.log_level:
        config.set('logging.level', args.log_level)
    
    if args.no_cache:
        config.set('cache.enabled', False)
    
    if args.parallel:
        config.set('processing.parallel_workers', 8)  # Increase workers
    
    if args.output:
        config.set('data.output_files.final', args.output)


def validate_data_only(processor: DataProcessor) -> bool:
    """Run data validation only."""
    logger = Logger(__name__)
    logger.info("Running data validation only...")
    
    try:
        # Load and validate all data
        data = processor.load_all_data()
        
        # Get validation summary
        validation_summary = processor.validator.get_validation_summary()
        
        # Print validation results
        print("\n" + "="*60)
        print("DATA VALIDATION SUMMARY")
        print("="*60)
        print(f"Total validations: {validation_summary['total_validations']}")
        print(f"Failed validations: {validation_summary['failed_validations']}")
        print(f"Success rate: {validation_summary['success_rate']:.2%}")
        
        if validation_summary['failed_validations'] > 0:
            print("\nFAILED VALIDATIONS:")
            for result in validation_summary['results']:
                if not result['valid']:
                    print(f"  - {result['name']}: {result['errors']}")
        
        print("="*60)
        
        return validation_summary['failed_validations'] == 0
        
    except Exception as e:
        logger.error(f"Validation failed: {e}")
        return False


def run_specific_stage(processor: DataProcessor, stage: str) -> Optional[object]:
    """Run a specific processing stage."""
    logger = Logger(__name__)
    
    if stage == '1':
        logger.info("Running Stage 1 only...")
        data = processor.load_all_data()
        result = processor.process_stage1(data)
        processor._save_intermediate_result(result, 'stage1')
        return result
    
    elif stage == '2':
        logger.info("Running Stage 2 only...")
        # Need to load stage 1 results
        stage1_file = config.get('data.output_files.stage1')
        if not Path(stage1_file).exists():
            logger.error(f"Stage 1 output file not found: {stage1_file}")
            logger.info("Running Stage 1 first...")
            data = processor.load_all_data()
            stage1_df = processor.process_stage1(data)
            processor._save_intermediate_result(stage1_df, 'stage1')
        else:
            stage1_df = processor.excel_loader.load_excel(stage1_file)
            data = processor.load_all_data()
        
        result = processor.process_stage2(stage1_df, data)
        processor._save_intermediate_result(result, 'stage2')
        return result
    
    elif stage == '3':
        logger.info("Running Stage 3 only...")
        # Need to load stage 2 results
        stage2_file = config.get('data.output_files.stage2')
        if not Path(stage2_file).exists():
            logger.error(f"Stage 2 output file not found: {stage2_file}")
            logger.info("Running full pipeline up to Stage 2...")
            data = processor.load_all_data()
            stage1_df = processor.process_stage1(data)
            stage2_df = processor.process_stage2(stage1_df, data)
            processor._save_intermediate_result(stage2_df, 'stage2')
        else:
            stage2_df = processor.excel_loader.load_excel(stage2_file)
            # Still need calendar data for time calculations
            data = processor.load_all_data()
        
        result = processor.process_stage3(stage2_df)
        processor._save_final_result(result)
        return result
    
    else:  # stage == 'all'
        logger.info("Running full pipeline...")
        return processor.run_full_pipeline()


def print_performance_stats(processor: DataProcessor, execution_time: float) -> None:
    """Print performance statistics."""
    stats = processor.get_processing_stats()
    
    print("\n" + "="*60)
    print("PERFORMANCE STATISTICS")
    print("="*60)
    print(f"Total execution time: {execution_time:.2f} seconds")
    
    # Cache statistics
    cache_info = stats.get('cache_info', {})
    if cache_info.get('cache_enabled'):
        print(f"Cache files: {cache_info.get('cache_files', 0)}")
        print(f"Cache size: {cache_info.get('total_size_mb', 0):.2f} MB")
    
    # Time calculator cache
    time_cache = stats.get('time_calculator_cache')
    if time_cache:
        print(f"Time calculator cache hits: {time_cache.hits}")
        print(f"Time calculator cache misses: {time_cache.misses}")
        print(f"Time calculator cache hit rate: {time_cache.hits / (time_cache.hits + time_cache.misses):.2%}")
    
    print("="*60)


def main() -> int:
    """Main application entry point."""
    args = parse_arguments()
    
    if args.help_full:
        print(__doc__)
        return 0
    
    # Setup configuration
    setup_configuration(args)
    
    # Initialize logger
    logger = Logger(__name__)
    logger.info("Starting Empty Return Analysis application...")
    
    try:
        # Initialize processor
        cache_enabled = not args.no_cache
        processor = DataProcessor(cache_enabled=cache_enabled)
        
        # Clear cache if requested
        if args.clear_cache:
            logger.info("Clearing all caches...")
            processor.clear_all_caches()
        
        # Run validation only if requested
        if args.validate_only:
            success = validate_data_only(processor)
            return 0 if success else 1
        
        # Run processing
        start_time = time.time()
        
        result = run_specific_stage(processor, args.stage)
        
        execution_time = time.time() - start_time
        
        if result is not None:
            logger.info(f"Processing completed successfully in {execution_time:.2f} seconds")
            logger.info(f"Final result contains {len(result)} records")
            
            # Print performance statistics
            print_performance_stats(processor, execution_time)
            
            return 0
        else:
            logger.error("Processing failed")
            return 1
    
    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
        return 130
    
    except Exception as e:
        logger.error(f"Application failed: {e}")
        logger.exception("Full traceback:")
        return 1


if __name__ == '__main__':
    sys.exit(main())
