"""
Configuration management for Empty Return Analysis project.
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
import yaml
import json


class ConfigManager:
    """Centralized configuration management."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config.yaml"
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        if os.path.exists(self.config_path):
            with open(self.config_path, 'r', encoding='utf-8') as f:
                if self.config_path.endswith('.yaml') or self.config_path.endswith('.yml'):
                    return yaml.safe_load(f)
                elif self.config_path.endswith('.json'):
                    return json.load(f)
        
        # Return default configuration
        return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration."""
        return {
            'data': {
                'input_files': {
                    'abnormal_supply': 'Abnormal supply.xlsx',
                    'part_info': 'Part Info.xlsx',
                    'production_premise': 'Production premise.xlsx',
                    'time_config': 'time_config.xlsx',
                    'auto_return_parts': '5X线自动返空零件清单.xlsx',
                    'gm_management': '地码管理.xlsx',
                    'engine_job_info': 'engine_job_info_2025-0701-0714.xlsx'
                },
                'output_files': {
                    'stage1': 'Empty Return Analysis.xlsx',
                    'stage2': 'Empty Return Analysis Update.xlsx',
                    'final': 'result.xlsx'
                },
                'cache_dir': 'cache',
                'backup_dir': 'backup'
            },
            'processing': {
                'task_types': ['常规供线', 'JIS供线', 'Inhouse Bin供线'],
                'time_format': '%m/%d/%Y %I:%M:%S %p',
                'max_gap_multiplier': 3,
                'chunk_size': 10000,
                'parallel_workers': 4
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file': 'empty_return_analysis.log',
                'max_size': '10MB',
                'backup_count': 5
            },
            'cache': {
                'enabled': True,
                'ttl': 3600,  # 1 hour
                'max_size': 1000
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key (supports dot notation)."""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value by key (supports dot notation)."""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self) -> None:
        """Save configuration to file."""
        with open(self.config_path, 'w', encoding='utf-8') as f:
            if self.config_path.endswith('.yaml') or self.config_path.endswith('.yml'):
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            elif self.config_path.endswith('.json'):
                json.dump(self.config, f, indent=2, ensure_ascii=False)


# Global configuration instance
config = ConfigManager()
