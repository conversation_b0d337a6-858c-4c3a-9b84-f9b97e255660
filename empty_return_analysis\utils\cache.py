"""
Caching utilities for improved performance.
"""

import time
import hashlib
import pickle
from typing import Any, Optional, Dict, Callable
from functools import wraps
from pathlib import Path

from ..config import config
from .logger import Logger


class CacheManager:
    """In-memory cache manager with TTL support."""
    
    def __init__(self):
        self.logger = Logger(__name__)
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.enabled = config.get('cache.enabled', True)
        self.default_ttl = config.get('cache.ttl', 3600)
        self.max_size = config.get('cache.max_size', 1000)
    
    def _generate_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments."""
        key_data = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _is_expired(self, entry: Dict[str, Any]) -> bool:
        """Check if cache entry is expired."""
        if 'expires_at' not in entry:
            return False
        return time.time() > entry['expires_at']
    
    def _cleanup_expired(self) -> None:
        """Remove expired entries from cache."""
        expired_keys = [
            key for key, entry in self.cache.items() 
            if self._is_expired(entry)
        ]
        for key in expired_keys:
            del self.cache[key]
    
    def _enforce_size_limit(self) -> None:
        """Enforce maximum cache size by removing oldest entries."""
        if len(self.cache) <= self.max_size:
            return
        
        # Sort by access time and remove oldest entries
        sorted_items = sorted(
            self.cache.items(),
            key=lambda x: x[1].get('accessed_at', 0)
        )
        
        items_to_remove = len(self.cache) - self.max_size
        for i in range(items_to_remove):
            key = sorted_items[i][0]
            del self.cache[key]
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if not self.enabled:
            return None
        
        if key not in self.cache:
            return None
        
        entry = self.cache[key]
        if self._is_expired(entry):
            del self.cache[key]
            return None
        
        # Update access time
        entry['accessed_at'] = time.time()
        return entry['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache."""
        if not self.enabled:
            return
        
        ttl = ttl or self.default_ttl
        expires_at = time.time() + ttl if ttl > 0 else None
        
        self.cache[key] = {
            'value': value,
            'created_at': time.time(),
            'accessed_at': time.time(),
            'expires_at': expires_at
        }
        
        # Cleanup and enforce limits
        self._cleanup_expired()
        self._enforce_size_limit()
    
    def delete(self, key: str) -> bool:
        """Delete key from cache."""
        if key in self.cache:
            del self.cache[key]
            return True
        return False
    
    def clear(self) -> None:
        """Clear all cache entries."""
        self.cache.clear()
        self.logger.info("Cache cleared")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_entries = len(self.cache)
        expired_entries = sum(1 for entry in self.cache.values() if self._is_expired(entry))
        
        return {
            'enabled': self.enabled,
            'total_entries': total_entries,
            'expired_entries': expired_entries,
            'active_entries': total_entries - expired_entries,
            'max_size': self.max_size,
            'default_ttl': self.default_ttl
        }


def cached(ttl: Optional[int] = None, key_func: Optional[Callable] = None):
    """
    Decorator for caching function results.
    
    Args:
        ttl: Time to live in seconds
        key_func: Custom function to generate cache key
    """
    cache_manager = CacheManager()
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}_{cache_manager._generate_key(*args, **kwargs)}"
            
            # Try to get from cache
            result = cache_manager.get(cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl)
            
            return result
        
        # Add cache management methods to function
        wrapper.cache_clear = lambda: cache_manager.clear()
        wrapper.cache_stats = lambda: cache_manager.get_stats()
        
        return wrapper
    
    return decorator
