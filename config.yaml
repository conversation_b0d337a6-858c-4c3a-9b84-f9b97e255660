# Empty Return Analysis Configuration File

# Data file paths and settings
data:
  input_files:
    abnormal_supply: "Abnormal supply.xlsx"
    part_info: "Part Info.xlsx"
    production_premise: "Production premise.xlsx"
    time_config: "time_config.xlsx"
    auto_return_parts: "5X线自动返空零件清单.xlsx"
    gm_management: "地码管理.xlsx"
    engine_job_info: "engine_job_info_2025-0701-0714.xlsx"
  
  output_files:
    stage1: "Empty Return Analysis.xlsx"
    stage2: "Empty Return Analysis Update.xlsx"
    final: "result.xlsx"
  
  cache_dir: "cache"
  backup_dir: "backup"

# Processing settings
processing:
  task_types:
    - "常规供线"
    - "JIS供线"
    - "Inhouse Bin供线"
  
  time_format: "%m/%d/%Y %I:%M:%S %p"
  max_gap_multiplier: 3
  chunk_size: 10000
  parallel_workers: 4

# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "empty_return_analysis.log"
  max_size: "10MB"
  backup_count: 5

# Cache settings
cache:
  enabled: true
  ttl: 3600  # 1 hour in seconds
  max_size: 1000  # Maximum number of cache entries

# Performance optimization settings
optimization:
  use_parallel_loading: true
  use_vectorized_calculations: true
  enable_memory_optimization: true
  
# Business rules and validation
validation:
  required_columns:
    abnormal_supply:
      - "Task Type"
      - "Lineback Time"
      - "Last Lineback Time"
      - "Part"
    part_info:
      - "Part"
      - "PN_EN"
      - "PN_CN"
      - "FD"
      - "MSP"
      - "Car_Model_Summary"
  
  data_quality_checks:
    check_negative_gaps: true
    check_invalid_task_types: true
    check_time_order: true
    check_missing_values: true
