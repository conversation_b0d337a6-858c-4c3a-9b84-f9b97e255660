# 迁移指南：从旧架构到新架构

本指南帮助您从原有的三个独立脚本（main.py、mainV1.py、mainV2.py）迁移到新的模块化架构。

## 迁移概述

### 原有架构
```
项目根目录/
├── main.py                    # 阶段1处理
├── mainV1.py                  # 阶段2处理  
├── mainV2.py                  # 阶段3处理
├── combine.py                 # 脚本执行器
├── *.xlsx                     # 数据文件
└── result.xlsx                # 最终结果
```

### 新架构
```
项目根目录/
├── empty_return_analysis/     # 主包
│   ├── core/                  # 核心处理模块
│   └── utils/                 # 工具模块
├── main_app.py               # 统一入口
├── config.yaml               # 配置文件
├── requirements.txt          # 依赖管理
└── *.xlsx                    # 数据文件
```

## 迁移步骤

### 步骤1：备份现有代码和数据

```bash
# 创建备份目录
mkdir backup_$(date +%Y%m%d)

# 备份Python脚本
cp *.py backup_$(date +%Y%m%d)/

# 备份数据文件
cp *.xlsx backup_$(date +%Y%m%d)/
```

### 步骤2：安装新架构

```bash
# 安装依赖
pip install -r requirements.txt

# 验证安装
python -c "import empty_return_analysis; print('安装成功')"
```

### 步骤3：配置文件设置

#### 检查数据文件路径
确保`config.yaml`中的文件路径与您的实际文件匹配：

```yaml
data:
  input_files:
    abnormal_supply: "Abnormal supply.xlsx"        # 检查文件名
    part_info: "Part Info.xlsx"                    # 检查文件名
    production_premise: "Production premise.xlsx"  # 检查文件名
    time_config: "time_config.xlsx"               # 检查文件名
    # ... 其他文件
```

#### 自定义配置（可选）
如果您的文件名不同，可以创建自定义配置文件：

```bash
cp config.yaml my_config.yaml
# 编辑my_config.yaml中的文件路径
```

### 步骤4：验证数据兼容性

运行数据验证以确保数据格式兼容：

```bash
python main_app.py --validate-only
```

如果验证失败，请检查：
- 文件路径是否正确
- Excel文件格式是否正确
- 必需的列是否存在

### 步骤5：测试运行

#### 分阶段测试
```bash
# 测试阶段1
python main_app.py --stage 1

# 测试阶段2
python main_app.py --stage 2

# 测试阶段3
python main_app.py --stage 3
```

#### 完整流程测试
```bash
python main_app.py
```

### 步骤6：结果对比验证

#### 运行旧代码生成基准结果
```bash
cd backup_$(date +%Y%m%d)
python combine.py
cp result.xlsx ../result_old.xlsx
cd ..
```

#### 运行新代码
```bash
python main_app.py
```

#### 对比结果
```python
import pandas as pd

# 加载结果文件
old_result = pd.read_excel('result_old.xlsx')
new_result = pd.read_excel('result.xlsx')

# 基本统计对比
print("旧结果行数:", len(old_result))
print("新结果行数:", len(new_result))

# 关键列对比
key_columns = ['Empty_Return_Gap', 'last_last_vs_last_gap', 'Staging_Time']
for col in key_columns:
    if col in old_result.columns and col in new_result.columns:
        old_mean = old_result[col].mean()
        new_mean = new_result[col].mean()
        print(f"{col} - 旧平均值: {old_mean:.2f}, 新平均值: {new_mean:.2f}")
```

## 功能对应关系

### 原main.py → 新架构
```python
# 原代码
def main():
    task_df = pd.read_excel('Abnormal supply.xlsx')
    # ... 处理逻辑
    task_df.to_excel('Empty Return Analysis.xlsx', index=False)

# 新架构
processor = DataProcessor()
data = processor.load_all_data()
stage1_result = processor.process_stage1(data)
```

### 原mainV1.py → 新架构
```python
# 原代码
def main():
    main_df = pd.read_excel('Empty Return Analysis.xlsx')
    # ... 处理逻辑
    filtered_df.to_excel('Empty Return Analysis Update.xlsx', index=False)

# 新架构
stage2_result = processor.process_stage2(stage1_result, data)
```

### 原mainV2.py → 新架构
```python
# 原代码
df = pd.read_excel('Empty Return Analysis Update.xlsx')
# ... 处理逻辑
df.to_excel('result.xlsx', index=False)

# 新架构
final_result = processor.process_stage3(stage2_result)
```

### 原combine.py → 新架构
```python
# 原代码
scripts = ["main.py", "mainV1.py", "mainV2.py"]
for script in scripts:
    subprocess.run([sys.executable, script])

# 新架构
python main_app.py  # 一个命令完成所有处理
```

## 配置迁移

### 硬编码参数 → 配置文件

#### 原代码中的硬编码参数
```python
# main.py
task_types = ['常规供线', 'JIS供线', 'Inhouse Bin供线']
time_format = '%m/%d/%Y %I:%M:%S %p'

# mainV1.py
max_gap_multiplier = 3
```

#### 新架构配置文件
```yaml
processing:
  task_types:
    - "常规供线"
    - "JIS供线" 
    - "Inhouse Bin供线"
  time_format: "%m/%d/%Y %I:%M:%S %p"
  max_gap_multiplier: 3
```

### 文件路径 → 配置管理

#### 原代码
```python
task_df = pd.read_excel('Abnormal supply.xlsx')
part_info_df = pd.read_excel('Part Info.xlsx')
```

#### 新架构
```yaml
data:
  input_files:
    abnormal_supply: "Abnormal supply.xlsx"
    part_info: "Part Info.xlsx"
```

## 性能对比

### 预期性能提升

| 指标 | 原架构 | 新架构 | 提升幅度 |
|------|--------|--------|----------|
| 总执行时间 | 100% | 20-40% | 60-80%减少 |
| 内存使用 | 100% | 40-60% | 40-60%减少 |
| 文件加载时间 | 100% | 20-40% | 60-80%减少 |
| 错误处理 | 基础 | 完善 | 显著改善 |
| 可维护性 | 低 | 高 | 显著改善 |

### 性能测试

运行性能对比测试：

```bash
# 测试新架构性能
time python main_app.py

# 查看详细性能统计
python main_app.py --log-level DEBUG
```

## 故障排除

### 常见迁移问题

#### 1. 文件路径错误
**错误信息**: `FileNotFoundError: [Errno 2] No such file or directory`

**解决方案**:
```bash
# 检查文件是否存在
ls -la *.xlsx

# 更新配置文件中的路径
vim config.yaml
```

#### 2. 列名不匹配
**错误信息**: `KeyError: 'Column_Name'`

**解决方案**:
```python
# 检查Excel文件的列名
import pandas as pd
df = pd.read_excel('your_file.xlsx')
print(df.columns.tolist())
```

#### 3. 数据格式问题
**错误信息**: `ValueError: time data does not match format`

**解决方案**:
```yaml
# 在config.yaml中调整时间格式
processing:
  time_format: "%Y-%m-%d %H:%M:%S"  # 根据实际格式调整
```

#### 4. 内存不足
**错误信息**: `MemoryError`

**解决方案**:
```yaml
# 减少处理块大小
processing:
  chunk_size: 5000  # 从默认10000减少到5000
  parallel_workers: 2  # 减少并行worker数量
```

### 回滚方案

如果迁移遇到问题，可以快速回滚到原架构：

```bash
# 停止新架构
# 恢复原代码
cp backup_$(date +%Y%m%d)/*.py .

# 运行原架构
python combine.py
```

## 迁移检查清单

### 迁移前检查
- [ ] 备份所有原始代码和数据
- [ ] 确认Python版本兼容性（3.8+）
- [ ] 检查磁盘空间充足
- [ ] 记录当前处理时间作为基准

### 迁移过程检查
- [ ] 成功安装新架构依赖
- [ ] 配置文件路径正确
- [ ] 数据验证通过
- [ ] 分阶段测试成功
- [ ] 完整流程测试成功

### 迁移后验证
- [ ] 结果数据一致性验证
- [ ] 性能提升确认
- [ ] 错误处理测试
- [ ] 文档和配置更新
- [ ] 团队培训完成

## 后续优化建议

### 1. 定期维护
- 定期清理缓存文件
- 监控性能指标
- 更新依赖包版本

### 2. 进一步定制
- 根据实际需求调整配置
- 添加自定义业务规则
- 集成到现有工作流

### 3. 扩展功能
- 添加数据可视化
- 集成数据库存储
- 开发Web界面

## 支持和帮助

如果在迁移过程中遇到问题：

1. 查看详细日志：`python main_app.py --log-level DEBUG`
2. 运行数据验证：`python main_app.py --validate-only`
3. 检查配置文件格式
4. 参考故障排除部分
5. 联系技术支持团队

迁移完成后，您将享受到更好的性能、更强的可维护性和更丰富的功能。
