"""
Main data processing pipeline.
"""

import pandas as pd
import numpy as np
import re
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

from ..utils.excel_loader import ExcelLoader
from ..utils.data_validator import DataValidator
from ..utils.logger import Logger
from ..utils.cache import cached
from .time_calculator import TimeCalculator
from ..config import config


class DataProcessor:
    """Main data processing pipeline for Empty Return Analysis."""
    
    def __init__(self, cache_enabled: bool = True):
        self.logger = Logger(__name__)
        self.excel_loader = ExcelLoader(cache_enabled)
        self.validator = DataValidator()
        self.time_calculator = None
        self.config = config
        
        # Translation mappings
        self.task_type_translation = {
            '常规供线': 'Normal Supply',
            'JIS供线': 'JIS Supply',
            'Inhouse Bin供线': 'Inhouse Bin Supply'
        }
        
        self.part_translation = {
            '左后门板G48': 'LH Rear Door Panel G48',
            '右后门板G48': 'RH Rear Door Panel G48',
            '右后门板G28': 'RH Rear Door Panel G28',
            '左后门板G28': 'LH Rear Door Panel G28',
            'G48左前门板': 'LH Front Door Panel G48',
            'G48右前门板': 'RH Front Door Panel G48',
            'G28左前门板': 'LH Front Door Panel G28',
            'G28右前门板': 'RH Front Door Panel G28',
            '顶饰条L': 'Strip Roof LH',
            '顶饰条R': 'Strip Roof RH',
            '主线束G28': 'Main Harness G28',
            '主线束G48': 'Main Harness G48',
            '中控台G28': 'Center Console G28',
            '中控台G48': 'Center Console G48',
            '上扰流板G48': 'Spoiler Trunk Lid G48',
            'G48座椅侧翼右': 'Bolster RH G48',
            'G48座椅侧翼左': 'Bolster LH G48',
            'G48方向盘': 'Steering Wheel G48',
            '仪表板G28': 'IP G28',
            '仪表板G48': 'IP G48',
            '车外后视镜_副驾G48': 'Out Mirror Driver G48',
            '车外后视镜_主驾G48': 'Out Mirror Passenger G48',
            '仪表板线束 G28': 'IP Harness G28',
            '双肾格栅G28BEV': 'Kidney Grille G28BEV'
        }
    
    def load_all_data(self) -> Dict[str, pd.DataFrame]:
        """Load all required data files."""
        self.logger.info("Loading all data files...")
        
        file_paths = {
            'abnormal_supply': self.config.get('data.input_files.abnormal_supply'),
            'part_info': self.config.get('data.input_files.part_info'),
            'production_premise': self.config.get('data.input_files.production_premise'),
            'time_config': self.config.get('data.input_files.time_config'),
            'auto_return_parts': self.config.get('data.input_files.auto_return_parts'),
            'gm_management': self.config.get('data.input_files.gm_management'),
            'engine_job_info': self.config.get('data.input_files.engine_job_info')
        }
        
        # Load files in parallel
        data = self.excel_loader.load_multiple_excel(file_paths)
        
        # Validate loaded data
        self._validate_loaded_data(data)
        
        # Initialize time calculator
        self.time_calculator = TimeCalculator(data['time_config'])
        
        return data
    
    def _validate_loaded_data(self, data: Dict[str, pd.DataFrame]) -> None:
        """Validate all loaded data."""
        self.logger.info("Validating loaded data...")
        
        # Define validation rules for each dataset
        validation_rules = {
            'abnormal_supply': {
                'required_columns': ['Task Type', 'Lineback Time', 'Last Lineback Time', 'Part'],
                'column_types': {'Task Type': 'object', 'Part': 'object'}
            },
            'part_info': {
                'required_columns': ['Part', 'PN_EN', 'PN_CN', 'FD', 'MSP', 'Car_Model_Summary'],
                'column_types': {'Part': 'object', 'FD': 'float', 'MSP': 'float'}
            },
            'time_config': {
                'required_columns': ['Date', 'Start Time', 'End Time'],
                'column_types': {'Date': 'datetime64[ns]'}
            }
        }
        
        for name, df in data.items():
            rules = validation_rules.get(name, {})
            self.validator.validate_dataframe(df, name, **rules)
    
    def process_stage1(self, data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        Process stage 1: Basic data cleaning and time calculations.
        
        This corresponds to the original main.py functionality.
        """
        self.logger.info("Starting Stage 1 processing...")
        
        task_df = data['abnormal_supply'].copy()
        part_info_df = data['part_info']
        production_df = data['production_premise']
        
        # Data cleaning
        allowed_task_types = self.config.get('processing.task_types')
        task_df = task_df[task_df['Task Type'].isin(allowed_task_types)]
        task_df = task_df.dropna(subset=['Lineback Time', 'Last Lineback Time'])
        
        self.logger.info(f"After filtering: {len(task_df)} records")
        
        # Calculate Empty_Return_Gap
        task_df = self.time_calculator.calculate_time_differences_vectorized(
            task_df, 'Last Lineback Time', 'Lineback Time', 'Empty_Return_Gap'
        )
        
        # Merge part information
        part_info_columns = ['Part', 'PN_EN', 'PN_CN', 'FD', 'MSP', 'Car_Model_Summary', 'Fitment_Point']
        task_df = task_df.merge(part_info_df[part_info_columns], on='Part', how='left')
        
        # Calculate bin cover times
        task_df = self._calculate_bin_cover_times(task_df, production_df)
        
        # Format time columns
        task_df = self._format_time_columns(task_df)
        
        # Reorder columns
        task_df = self._reorder_stage1_columns(task_df)
        
        self.logger.info("Stage 1 processing completed")
        return task_df
    
    def _calculate_bin_cover_times(self, df: pd.DataFrame, production_df: pd.DataFrame) -> pd.DataFrame:
        """Calculate 1Bin and 2Bins cover times."""
        # Create production frequency mapping
        prod_freq_map = dict(zip(production_df.iloc[:, 0], production_df.iloc[:, 1]))
        
        def compute_1bin_cover(row):
            try:
                model = row['Car_Model_Summary']
                fd = row['FD']
                freq = prod_freq_map.get(model, None)
                if freq and freq > 0 and pd.notna(fd):
                    return round((60 / freq) * fd, 2)
            except:
                return None
            return None
        
        df['1Bin_Cover_Time'] = df.apply(compute_1bin_cover, axis=1)
        df['2Bins_Cover_Time'] = df['1Bin_Cover_Time'] * 2
        
        return df
    
    def _format_time_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Format time columns to standard format."""
        time_columns = [
            'Create Time', 'Lineback Time', 'Last Lineback Time', 
            'Start Time', 'Finish Time', 'Agv Arrival Staging', 'AGV Staging LS Task Exe'
        ]
        
        time_format = self.config.get('processing.time_format')
        
        for col in time_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col]).dt.strftime(time_format)
        
        return df
    
    def _reorder_stage1_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Reorder columns for stage 1 output."""
        output_columns = [
            'Task No.', 'Task Type', 'status', 'Task Status', 'Part', 'Rack No.',
            'Source Location', 'Start Area', 'Destination Area', 'Create Time',
            'Start Time', 'Finish Time', 'Agv Arrival Staging', 'AGV Staging LS Task Exe',
            'Lineback Time', 'Last Lineback Time', 'Empty_Return_Gap',
            'PN_EN', 'PN_CN', 'FD', 'MSP', 'Car_Model_Summary',
            '1Bin_Cover_Time', '2Bins_Cover_Time', 'Fitment_Point'
        ]
        
        # Only include columns that exist in the DataFrame
        available_columns = [col for col in output_columns if col in df.columns]
        return df[available_columns]
    
    def process_stage2(self, 
                      stage1_df: pd.DataFrame, 
                      data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        Process stage 2: Add translations, destinations, and additional data.
        
        This corresponds to the original mainV1.py functionality.
        """
        self.logger.info("Starting Stage 2 processing...")
        
        df = stage1_df.copy()
        auto_return_parts = data['auto_return_parts']
        gm_df = data['gm_management']
        engine_job_df = data['engine_job_info']
        
        # Add translations
        df = self._add_translations(df)
        
        # Determine destinations
        df = self._determine_destinations(df)
        
        # Add automatic return information
        df = self._add_automatic_return_info(df, auto_return_parts)
        
        # Replace spaces in column names with underscores
        df.columns = df.columns.str.replace(' ', '_')
        
        # Filter data
        df = self._filter_stage2_data(df)
        
        # Calculate CV (Coefficient of Variation)
        df = self._calculate_cv(df)
        
        # Process quality control data
        df = self._process_quality_control_data(df, gm_df)
        
        # Calculate 3Bins cover time
        df['3Bins_Cover_Time'] = df['1Bin_Cover_Time'] * 3
        
        # Merge engine job data
        df = self._merge_engine_job_data(df, engine_job_df)
        
        self.logger.info("Stage 2 processing completed")
        return df

    def _add_translations(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add English translations for task types and parts."""
        # Add task type translations
        df['Task_Type_EN'] = df['Task Type'].map(self.task_type_translation)

        # Add part translations (only for JIS supply)
        df['Part_EN'] = df.apply(
            lambda x: self.part_translation.get(x['Part'], x['Part'])
            if x['Task Type'] == 'JIS供线' else x['Part'],
            axis=1
        )

        return df

    def _determine_destinations(self, df: pd.DataFrame) -> pd.DataFrame:
        """Determine destination types based on business rules."""
        def get_destination(row):
            task_type = row['Task Type']
            dest_area = row['Destination Area']

            if task_type == 'Inhouse Bin供线':
                return 'LINE'
            elif task_type == 'JIS供线':
                if dest_area.startswith('LINE'):
                    return 'LINE'
                elif dest_area.startswith('SUMA'):
                    return 'SUMA'
            elif task_type == '常规供线':
                if len(dest_area) >= 5 and dest_area[4] in ('K', 'S'):
                    return 'SUMA'
                elif len(dest_area) >= 5 and dest_area[4] in ('U', 'F'):
                    return 'TU'
                elif dest_area.startswith(('TU', 'WTU')):
                    return 'TU'
                elif dest_area.startswith('W') and len(dest_area) >= 2 and dest_area[1].isdigit():
                    return 'LINE'

            return 'UNKNOWN'

        df['Destination'] = df.apply(get_destination, axis=1)
        return df

    def _add_automatic_return_info(self, df: pd.DataFrame, auto_return_parts: pd.DataFrame) -> pd.DataFrame:
        """Add automatic return information."""
        automatic_return_part_list = auto_return_parts['零件号'].tolist()
        df['Automatic_Empty_Return'] = df['Part'].apply(
            lambda x: 'Automatic Return' if x in automatic_return_part_list else 'Non-Automatic Return'
        )
        return df

    def _filter_stage2_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Filter data for stage 2 processing."""
        # Filter out records with null Empty_Return_Gap
        initial_count = len(df)
        df = df[df['Empty_Return_Gap'].notna()]

        # Filter based on gap threshold
        max_gap_multiplier = self.config.get('processing.max_gap_multiplier', 3)
        df = df[df['Empty_Return_Gap'] <= max_gap_multiplier * df['1Bin_Cover_Time']]

        final_count = len(df)
        self.logger.info(f"Filtered data: {initial_count} -> {final_count} records")

        return df

    def _calculate_cv(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate Coefficient of Variation (CV) for each part."""
        # Calculate CV by Part_EN
        cv_df = df.groupby('Part_EN')['Empty_Return_Gap'].agg(['std', 'mean']).reset_index()

        # Filter out zero means to avoid division by zero
        cv_df = cv_df[cv_df['mean'] != 0]

        # Calculate CV
        cv_df['CV'] = (cv_df['std'] / cv_df['mean']) * 100

        # Merge back to main dataframe
        df = df.merge(cv_df[['Part_EN', 'CV']], on='Part_EN', how='left')

        return df

    def _process_quality_control_data(self, df: pd.DataFrame, gm_df: pd.DataFrame) -> pd.DataFrame:
        """Process quality control data and merge QR quantities."""
        # Process GM data
        gm_df = gm_df.copy()
        gm_df['PN_WO_AI'] = gm_df['当前零件'].apply(
            lambda x: x[:7] if isinstance(x, str) and not re.search('[\u4e00-\u9fa5]', x) else x
        )

        # Count QR quantities
        qr_count = gm_df[gm_df['业务类型'] == '线边作业点'].groupby('PN_WO_AI').size().reset_index(name='QR_Qty')

        # Process main dataframe
        df['PN_WO_AI'] = df['Part'].apply(
            lambda x: x[:7] if not re.search('[\u4e00-\u9fa5]', x) else x
        )

        # Merge QR data
        df = df.merge(qr_count, on='PN_WO_AI', how='left')
        df['QR_Qty'] = df['QR_Qty'].fillna(0).astype(int)

        # Apply special rules
        df.loc[(df['Part'] == '5B36BF9-01'), 'QR_Qty'] = 2
        df.loc[
            (df['Part_EN'] == 'Main Harness G28') |
            (df['Part_EN'] == 'Main Harness G48'),
            'QR_Qty'
        ] = 2

        return df

    def _merge_engine_job_data(self, df: pd.DataFrame, engine_job_df: pd.DataFrame) -> pd.DataFrame:
        """Merge engine job data."""
        # Merge to add last_last_lineback_time
        df = df.merge(
            engine_job_df[['biz_task_id', 'last_last_lineback_time']],
            left_on='Task_No.',
            right_on='biz_task_id',
            how='left'
        )

        # Format time column
        time_format = self.config.get('processing.time_format')
        df['last_last_lineback_time'] = pd.to_datetime(
            df['last_last_lineback_time']
        ).dt.strftime(time_format)

        # Remove redundant column
        df = df.drop(columns=['biz_task_id'], errors='ignore')

        return df

    def process_stage3(self, stage2_df: pd.DataFrame) -> pd.DataFrame:
        """
        Process stage 3: Final calculations and additional time differences.

        This corresponds to the original mainV2.py functionality.
        """
        self.logger.info("Starting Stage 3 processing...")

        df = stage2_df.copy()

        # Calculate last_last_vs_last_gap
        df['last_last_vs_last_gap'] = df.apply(
            lambda row: 0 if pd.isna(row['last_last_lineback_time'])
            else self.time_calculator.calculate_time_difference(
                row['last_last_lineback_time'],
                row['Last_Lineback_Time']
            ),
            axis=1
        )

        # Calculate Staging_Time
        df['Staging_Time'] = df.apply(
            lambda row: 0 if pd.isna(row['AGV_Staging_LS_Task_Exe'])
            else self.time_calculator.calculate_time_difference(
                row['Agv_Arrival_Staging'],
                row['AGV_Staging_LS_Task_Exe']
            ),
            axis=1
        )

        self.logger.info("Stage 3 processing completed")
        return df

    def run_full_pipeline(self) -> pd.DataFrame:
        """Run the complete data processing pipeline."""
        self.logger.info("Starting full data processing pipeline...")

        # Load all data
        data = self.load_all_data()

        # Stage 1: Basic processing
        stage1_df = self.process_stage1(data)
        self._save_intermediate_result(stage1_df, 'stage1')

        # Stage 2: Enhanced processing
        stage2_df = self.process_stage2(stage1_df, data)
        self._save_intermediate_result(stage2_df, 'stage2')

        # Stage 3: Final calculations
        final_df = self.process_stage3(stage2_df)
        self._save_final_result(final_df)

        self.logger.info("Full pipeline completed successfully")
        return final_df

    def _save_intermediate_result(self, df: pd.DataFrame, stage: str) -> None:
        """Save intermediate processing results."""
        output_file = self.config.get(f'data.output_files.{stage}')
        if output_file:
            df.to_excel(output_file, index=False)
            self.logger.info(f"Saved {stage} results to {output_file}")

    def _save_final_result(self, df: pd.DataFrame) -> None:
        """Save final processing results."""
        output_file = self.config.get('data.output_files.final')
        if output_file:
            df.to_excel(output_file, index=False)
            self.logger.info(f"Saved final results to {output_file}")

    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics and performance metrics."""
        return {
            'cache_info': self.excel_loader.get_cache_info(),
            'validation_summary': self.validator.get_validation_summary(),
            'time_calculator_cache': self.time_calculator.get_working_hours_for_day.cache_info() if self.time_calculator else None
        }

    def clear_all_caches(self) -> None:
        """Clear all caches."""
        self.excel_loader.clear_cache()
        if self.time_calculator:
            self.time_calculator.clear_cache()
        self.logger.info("All caches cleared")
